-- 彩票系统客户端主文件

-- 调试打印函数
local function DebugPrint(message, level)
    -- 只有在调试模式开启时才打印
    if Config.Debug and Config.Debug.enabled then
        level = level or "^7" -- 默认白色
        print(level .. "[彩票系统-调试] " .. message)
    end
end

local isUIOpen = false
local currentShop = nil
local prizePoolUpdateThread = nil -- 奖池更新线程控制变量

-- 创建彩票店
CreateThread(function()
    for shopId, shop in pairs(Config.Shops) do
        -- 创建地图标记
        if shop.createBlip == true and shop.blip then
            local blip = AddBlipForCoord(shop.coords.x, shop.coords.y, shop.coords.z)
            SetBlipSprite(blip, shop.blip.sprite)
            SetBlipColour(blip, shop.blip.color)
            SetBlipScale(blip, shop.blip.scale)
            SetBlipAsShortRange(blip, true)
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString(shop.blip.name)
            EndTextCommandSetBlipName(blip)
            DebugPrint("^2[彩票系统] ^7彩票店BLIP创建成功: " .. shop.name)
        else
            DebugPrint("^3[彩票系统] ^7彩票店BLIP创建已禁用: " .. shop.name)
        end
        
        -- 创建NPC
        if shop.createPed ~= false and shop.ped then
            RequestModel(GetHashKey(shop.ped.model))
            while not HasModelLoaded(GetHashKey(shop.ped.model)) do
                Wait(1)
            end
            
            local ped = CreatePed(4, GetHashKey(shop.ped.model), shop.ped.coords.x, shop.ped.coords.y, shop.ped.coords.z, shop.ped.heading, false, true)
            -- 设置NPC为完全无敌
            SetEntityCanBeDamaged(ped, false)
            SetEntityInvincible(ped, true)
            SetPedCanRagdollFromPlayerImpact(ped, false)
            SetBlockingOfNonTemporaryEvents(ped, true)
            SetPedFleeAttributes(ped, 0, 0)
            SetPedCombatAttributes(ped, 17, 1)
            SetPedCanBeTargetted(ped, false)
            SetPedCanBeKnockedOffVehicle(ped, 1)
            SetPedCanBeDraggedOut(ped, false)
            SetEntityProofs(ped, true, true, true, true, true, true, true, true)
            SetPedRandomComponentVariation(ped, false)
            
            if shop.ped.scenario then
                TaskStartScenarioInPlace(ped, shop.ped.scenario, 0, true)
            end
            
            -- 设置目标交互
            if GetResourceState('ox_target') == 'started' then
                exports.ox_target:addLocalEntity(ped, {
                    {
                        name = 'lottery_shop_' .. shopId,
                        icon = 'fas fa-ticket-alt',
                        label = '购买彩票',
                        onSelect = function()
                            OpenLotteryShop(shopId)
                        end
                    }
                })
            elseif GetResourceState('qb-target') == 'started' then
                exports['qb-target']:AddTargetEntity(ped, {
                    options = {
                        {
                            type = "client",
                            event = "lottery:openShop",
                            icon = "fas fa-ticket-alt",
                            label = "购买彩票",
                            shopId = shopId
                        }
                    },
                    distance = 2.0
                })
            else
                -- 使用文字UI作为备选方案
                CreateThread(function()
                    while DoesEntityExist(ped) do
                        local playerCoords = GetEntityCoords(PlayerPedId())
                        local pedCoords = GetEntityCoords(ped)
                        local distance = #(playerCoords - pedCoords)
                        
                        if distance < 2.0 then
                            -- 显示文字提示
                            SetTextComponentFormat("STRING")
                            AddTextComponentString("按 ~INPUT_CONTEXT~ 购买彩票")
                            DisplayHelpTextFromStringLabel(0, 0, 1, -1)
                            
                            if IsControlJustReleased(0, 51) then -- E键
                                OpenLotteryShop(shopId)
                            end
                        end
                        Wait(0)
                    end
                end)
            end
            
            DebugPrint("^2[彩票系统] ^7彩票店NPC创建成功: " .. shop.name)
        else
            -- 如果不创建NPC，则创建一个交互点
            DebugPrint("^3[彩票系统] ^7彩票店NPC创建已禁用: " .. shop.name .. "，创建交互点")
            CreateInteractionPoint(shopId, shop)
        end
    end
end)

-- 创建交互点函数
function CreateInteractionPoint(shopId, shop)
    local coords = shop.coords
    
    -- 使用ox_target创建交互区域
    if GetResourceState('ox_target') == 'started' then
        exports.ox_target:addSphereZone({
            coords = coords,
            radius = 1.5,
            options = {
                {
                    name = 'lottery_shop_zone_' .. shopId,
                    icon = 'fas fa-ticket-alt',
                    label = '购买彩票',
                    onSelect = function()
                        OpenLotteryShop(shopId)
                    end
                }
            }
        })
        DebugPrint("^2[彩票系统] ^7彩票店交互点(ox_target)创建成功: " .. shop.name)
    elseif GetResourceState('qb-target') == 'started' then
        -- 使用qb-target创建交互区域
        exports['qb-target']:AddCircleZone('lottery_shop_zone_' .. shopId, coords, 1.5, {
            name = 'lottery_shop_zone_' .. shopId,
            debugPoly = false,
        }, {
            options = {
                {
                    type = "client",
                    event = "lottery:openShop",
                    icon = "fas fa-ticket-alt",
                    label = "购买彩票",
                    shopId = shopId
                },
            },
            distance = 2.0
        })
        DebugPrint("^2[彩票系统] ^7彩票店交互点(qb-target)创建成功: " .. shop.name)
    else
        -- 如果没有target系统，使用传统的3D文本交互
        CreateThread(function()
            while true do
                Citizen.Wait(0)
                
                -- 获取玩家位置
                local playerPed = PlayerPedId()
                local playerCoords = GetEntityCoords(playerPed)
                local distance = #(playerCoords - coords)
                
                -- 在交互范围内
                if distance < 2.0 then
                    -- 显示提示
                    DrawText3D(coords.x, coords.y, coords.z, "按 ~y~E~w~ 购买~b~彩票")
                    
                    -- 检测按键
                    if IsControlJustReleased(0, 38) then -- E键
                        OpenLotteryShop(shopId)
                    end
                end
            end
        end)
        DebugPrint("^2[彩票系统] ^7彩票店交互点(3D文本)创建成功: " .. shop.name)
    end
end

-- 目标系统事件
RegisterNetEvent('lottery:openShop', function(data)
    OpenLotteryShop(data.shopId)
end)

-- 打开彩票店
function OpenLotteryShop(shopId)
    if isUIOpen then return end

    currentShop = shopId
    TriggerServerEvent('lottery:openShop', shopId)

    -- 停止之前的更新线程（如果存在）
    if prizePoolUpdateThread then
        prizePoolUpdateThread = nil
    end

    -- 创建新的奖池更新线程
    prizePoolUpdateThread = Citizen.CreateThread(function()
        Wait(500) -- 短暂延迟确保UI已加载
        TriggerServerEvent('caipiaoc:getPrizePools')

        -- 每60秒自动更新一次奖池信息
        while isUIOpen and prizePoolUpdateThread do
            Wait(60000) -- 60秒
            if isUIOpen and prizePoolUpdateThread then
                TriggerServerEvent('caipiaoc:getPrizePools')
            end
        end
    end)
end

-- 关闭彩票店
function CloseLotteryShop()
    if not isUIOpen then return end

    isUIOpen = false
    currentShop = nil

    -- 停止奖池更新线程
    if prizePoolUpdateThread then
        prizePoolUpdateThread = nil
    end

    SetNuiFocus(false, false)
    SendNUIMessage({
        action = 'closeLottery'
    })
end

-- 监听服务器事件
RegisterNetEvent('lottery:openUI', function(data)
    if isUIOpen then return end
    
    isUIOpen = true
    SetNuiFocus(true, true)
    
    -- 确保数据格式正确
    if not data.action then
        data.action = 'openLottery'
    end
    
    -- 发送数据到NUI
    SendNUIMessage(data)
end)

RegisterNetEvent('lottery:openScratchGame', function(data)
    -- 预加载资源
    SendNUIMessage({
        action = 'prepareResources',
        cardType = data.cardType
    })
    
    -- 短暂延迟后打开游戏
    CreateThread(function()
        Wait(500) -- 等待资源加载
        SendNUIMessage({
            action = 'openScratchCard',
            ticketId = data.ticketId,
            cardType = data.cardType,
            cardName = data.cardName,
            ticketData = data.ticketData
        })
    end)
end)

RegisterNetEvent('lottery:scratchResult', function(data)
    SendNUIMessage(data)
end)

RegisterNetEvent('lottery:drawResult', function(data)
    SendNUIMessage(data)
end)

RegisterNetEvent('lottery:announcement', function(message, type)
    SendNUIMessage({
        action = 'showAnnouncement',
        message = message,
        type = type or 'info'
    })
end)

-- 通知系统
RegisterNetEvent('lottery:notification', function(title, message, type)
    -- 发送到NUI显示通知
    SendNUIMessage({
        action = 'showNotification',
        title = title,
        message = message,
        type = type or 'info'
    })
    
    -- 同时在控制台记录
    DebugPrint(string.format("^3[彩票系统通知] ^7%s: %s", title, message))
end)

-- ESC键关闭界面 - 简化版本，确保基本功能正常
CreateThread(function()
    while true do
        if isUIOpen then
            if IsControlJustReleased(0, 322) then -- ESC键
                -- 直接关闭界面，不再通过复杂的状态检查
                CloseLotteryShop()
            end
        end
        Wait(0)
    end
end)

-- 资源停止时清理
AddEventHandler('onResourceStop', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        if isUIOpen then
            CloseLotteryShop()
        end
    end
end)

-- 禁用某些按键 (当UI打开时)
CreateThread(function()
    while true do
        if isUIOpen then
            -- 禁用移动
            DisableControlAction(0, 30, true) -- A/D
            DisableControlAction(0, 31, true) -- S/W
            DisableControlAction(0, 32, true) -- W
            DisableControlAction(0, 33, true) -- S
            DisableControlAction(0, 34, true) -- A
            DisableControlAction(0, 35, true) -- D
            
            -- 禁用攻击
            DisableControlAction(0, 24, true) -- 攻击
            DisableControlAction(0, 257, true) -- 攻击2
            DisableControlAction(0, 25, true) -- 瞄准
            DisableControlAction(0, 263, true) -- 近战攻击
            
            -- 禁用载具控制
            DisableControlAction(0, 75, true) -- 载具退出
            
            -- 禁用武器轮盘
            DisableControlAction(0, 37, true) -- 武器轮盘
            
            -- 禁用电话
            DisableControlAction(0, 172, true) -- 电话向上
            DisableControlAction(0, 173, true) -- 电话向下
            DisableControlAction(0, 174, true) -- 电话向左
            DisableControlAction(0, 175, true) -- 电话向右
        end
        Wait(0)
    end
end)

-- 导出函数供其他资源使用
exports('IsLotteryUIOpen', function()
    return isUIOpen
end)

exports('OpenLotteryShop', OpenLotteryShop)
exports('CloseLotteryShop', CloseLotteryShop)

-- 添加客户端与服务器通信的网络事件
-- 奖池信息
RegisterNUICallback('getPrizePools', function(data, cb)
    TriggerServerEvent('caipiaoc:getPrizePools')
    cb({})
end)

-- 接收奖池信息并发送到UI
RegisterNetEvent('caipiaoc:receivePrizePools')
AddEventHandler('caipiaoc:receivePrizePools', function(pools)
    SendNUIMessage({
        action = 'updatePrizePools',
        pools = pools
    })
end)

-- 购买刮刮乐
RegisterNUICallback('buyScratchCard', function(data, cb)
    TriggerServerEvent('lottery:buyScratchCard', data)
    cb({})
end)

-- 购买双色球
RegisterNUICallback('buyDoubleBall', function(data, cb)
    TriggerServerEvent('lottery:buyDoubleBall', data)
    cb({})
end)

-- 购买大乐透
RegisterNUICallback('buySuperLotto', function(data, cb)
    TriggerServerEvent('lottery:buySuperLotto', data)
    cb({})
end)

-- 购买排列5
RegisterNUICallback('buyArrangeFive', function(data, cb)
    TriggerServerEvent('lottery:buyArrangeFive', data)
    cb({})
end)

-- 关闭彩票UI的NUI回调
RegisterNUICallback('closeLottery', function(data, cb)
    CloseLotteryShop()
    cb({})
end)

-- 接收购买结果
RegisterNetEvent('lottery:buyResult')
AddEventHandler('lottery:buyResult', function(result)
    SendNUIMessage({
        action = 'buyResult',
        result = result
    })
end)

-- 刮刮乐完成
RegisterNUICallback('finishScratch', function(data, cb)
    TriggerServerEvent('lottery:finishScratch', data)
    cb({})
end)

-- 刮刮乐关闭
RegisterNUICallback('closeScratchCard', function(data, cb)
    SetNuiFocus(false, false)
    isUIOpen = false
    TriggerServerEvent('lottery:closeScratchCard')
    cb({})
end)

-- 设置焦点
RegisterNUICallback('focus', function(data, cb)
    SetNuiFocus(data.focus, data.focus)
    if data.focus == false then
        isUIOpen = false
    end
    cb({})
end)

-- 刮奖区域
RegisterNUICallback('scratchArea', function(data, cb)
    TriggerServerEvent('lottery:scratchArea', data)
    cb({})
end)

-- 自动兑奖
RegisterNUICallback('autoClaimOnReveal', function(data, cb)
    TriggerServerEvent('lottery:autoClaimOnReveal', data)
    cb({})
end)

-- 获取刮刮乐统计
RegisterNUICallback('getScratchStats', function(data, cb)
    TriggerServerEvent('lottery:getScratchStats')
    cb({})
end)

-- 接收刮刮乐统计
RegisterNetEvent('lottery:receiveScratchStats')
AddEventHandler('lottery:receiveScratchStats', function(stats)
    -- 确保stats是数组
    if stats == nil then
        stats = {}
    end
    
    -- 发送到UI
    SendNUIMessage({
        action = 'updateScratchStats',
        stats = stats
    })
end)

-- 获取彩票记录
RegisterNUICallback('getPlayerTickets', function(data, cb)
    TriggerServerEvent('lottery:getPlayerTickets', data)
    cb({})
end)

-- 接收彩票记录
RegisterNetEvent('lottery:receivePlayerTickets')
AddEventHandler('lottery:receivePlayerTickets', function(tickets)
    SendNUIMessage({
        action = 'updatePlayerTickets',
        tickets = tickets
    })
end)

-- 获取开奖历史
RegisterNUICallback('getDrawHistory', function(data, cb)
    TriggerServerEvent('lottery:getDrawHistory', data)
    cb({})
end)

-- 接收开奖历史
RegisterNetEvent('lottery:receiveDrawHistory')
AddEventHandler('lottery:receiveDrawHistory', function(data)
    -- 发送到NUI
    SendNUIMessage({
        action = 'receiveDrawHistory',
        data = data
    })
end)

-- 检查彩票职业系统是否启用
RegisterNUICallback('checkLotteryJobEnabled', function(data, cb)
    cb({
        lotteryJobEnabled = Config.LotteryJob.enabled,
        prizeAmount = tonumber(data.prizeAmount) or 0  -- 返回奖金金额
    })
end)

-- 兑奖
RegisterNUICallback('claimPrize', function(data, cb)
    TriggerServerEvent('caipiaoc:claimPrize', data)
    cb({})
end)

-- 接收兑奖结果并发送到UI
RegisterNetEvent('caipiaoc:claimPrizeResult')
AddEventHandler('caipiaoc:claimPrizeResult', function(result)
    -- 发送到NUI
    SendNUIMessage({
        action = 'claimPrizeResult',
        result = result
    })
end)

-- 未兑奖奖品
RegisterNUICallback('getUnclaimedPrizes', function(data, cb)
    TriggerServerEvent('caipiaoc:getUnclaimedPrizes')
    cb({})
end)

-- 接收未兑奖奖品并发送到UI
RegisterNetEvent('caipiaoc:receiveUnclaimedPrizes')
AddEventHandler('caipiaoc:receiveUnclaimedPrizes', function(prizes)
    SendNUIMessage({
        action = 'updateUnclaimedPrizes',
        prizes = prizes
    })
end)

-- 管理系统UI处理
RegisterNetEvent('lottery:openAdminUI')
AddEventHandler('lottery:openAdminUI', function()
    -- 打开管理系统UI
    SetNuiFocus(true, true)
    
    -- 创建iframe加载admin.html
    SendNUIMessage({
        action = 'openAdminUI'
    })
end)

-- 显示通知
function ShowNotification(title, message, notifType)
    if not title then title = "通知" end
    if not notifType then notifType = "info" end
    
    -- 调试打印
    DebugPrint(string.format("^3[彩票系统通知] ^7%s: %s", title, message))
    
    -- 发送到NUI显示通知
    SendNUIMessage({
        action = 'showNotification',
        title = title,
        message = message,
        type = notifType
    })
end

-- 更新手续费分成设置
RegisterNetEvent('lottery:updateCommissionSettings')
AddEventHandler('lottery:updateCommissionSettings', function(data)
    SendNUIMessage({
        action = 'updateCommissionSettings',
        success = data.success,
        settings = data.settings
    })
end)

-- 管理系统兑奖请求
RegisterNUICallback('adminClaimPrize', function(data, cb)
    TriggerServerEvent('caipiaoc:adminClaimPrize', data)
    cb({})
end)

-- 刷新管理系统数据
RegisterNetEvent('lottery:refreshAdminData')
AddEventHandler('lottery:refreshAdminData', function()
    SendNUIMessage({
        action = 'refreshAdminData'
    })
end)

-- 员工数据更新
RegisterNetEvent('lottery:employeeUpdate')
AddEventHandler('lottery:employeeUpdate', function(data)
    SendNUIMessage({
        action = 'employeeUpdate',
        data = data
    })
end)

-- 显示彩票配置页面
RegisterNetEvent('lottery:showConfigPage')
AddEventHandler('lottery:showConfigPage', function()
    -- 检查管理系统是否已经打开
    SendNUIMessage({
        action = 'showConfigPage'
    })
end)

-- 打开管理系统并显示配置页面
RegisterNetEvent('lottery:openAdminAndShowConfig')
AddEventHandler('lottery:openAdminAndShowConfig', function()
    -- 先请求管理系统数据，这会打开管理界面
    TriggerServerEvent('lottery:getAdminData')

    -- 设置一个标记，表示需要显示配置页面
    SendNUIMessage({
        action = 'setShowConfigFlag'
    })
end)

-- 发放员工奖金
RegisterNUICallback('payEmployeeBonus', function(data, cb)
    TriggerServerEvent('caipiaoc:payEmployeeBonus', data)
    cb({})
end)

-- 添加NUI回调
RegisterNUICallback('getLotteryConfig', function(data, cb)
    TriggerServerEvent('lottery:getLotteryConfig')
    cb({})
end)

RegisterNUICallback('saveLotteryConfig', function(data, cb)
    TriggerServerEvent('lottery:saveLotteryConfig', data)
    cb({})
end)

RegisterNUICallback('saveAllLotteryConfig', function(data, cb)
    TriggerServerEvent('lottery:saveAllLotteryConfig', data)
    cb({})
end)

-- 保存彩票奖项配置的回调
RegisterNUICallback('savePrizeConfig', function(data, cb)
    TriggerServerEvent('lottery:savePrizeConfig', data)
    cb({})
end)

-- 保存刮刮乐配置的回调
RegisterNUICallback('saveScratchConfig', function(data, cb)
    TriggerServerEvent('lottery:saveScratchConfig', data)
    cb({})
end)

-- 保存刮刮乐权重配置的回调
RegisterNUICallback('saveScratchRates', function(data, cb)
    TriggerServerEvent('lottery:saveScratchRates', data)
    cb({})
end)

-- 保存开奖设置的回调
RegisterNUICallback('saveDrawSettings', function(data, cb)
    TriggerServerEvent('lottery:saveDrawSettings', data)
    cb({})
end)

-- 接收彩票配置
RegisterNetEvent('lottery:nui:receiveLotteryConfig')
AddEventHandler('lottery:nui:receiveLotteryConfig', function(data)
    DebugPrint("^3收到服务器发送的彩票配置数据")
    DebugPrint("^3数据结构: " .. json.encode(data))

    if data and data.config then
        DebugPrint("^2配置数据有效，发送到NUI")
        SendNUIMessage({
            action = 'receiveLotteryConfig',
            config = data.config
        })
        DebugPrint("^2已发送到NUI")
    else
        DebugPrint("^1错误：配置数据无效")
    end
end)

-- 接收彩票配置保存结果
RegisterNetEvent('lottery:nui:saveLotteryConfigResult')
AddEventHandler('lottery:nui:saveLotteryConfigResult', function(data)
    SendNUIMessage({
        action = 'saveLotteryConfigResult',
        success = data.success,
        message = data.message,
        config = data.config
    })
end)

-- 接收刮刮乐配置保存结果（保留兼容旧版）
RegisterNetEvent('lottery:nui:saveScratchConfigResult')
AddEventHandler('lottery:nui:saveScratchConfigResult', function(data)
    SendNUIMessage({
        action = 'saveScratchConfigResult',
        success = data.success,
        message = data.message,
        config = data.config
    })
end)

-- 接收刮刮乐权重配置保存结果
RegisterNetEvent('lottery:nui:saveScratchRatesResult')
AddEventHandler('lottery:nui:saveScratchRatesResult', function(data)
    -- 发送权重配置结果
    SendNUIMessage({
        action = 'saveScratchRatesResult',
        success = data.success,
        message = data.message,
        config = data.config
    })
    
    -- 同时发送兼容旧版的消息，确保UI能正确更新
    SendNUIMessage({
        action = 'saveScratchConfigResult',
        success = data.success,
        message = data.message,
        config = data.config
    })
end)

-- 接收彩票配置更新广播
RegisterNetEvent('lottery:refreshLotteryConfig')
AddEventHandler('lottery:refreshLotteryConfig', function(config)
    -- 更新客户端缓存的配置
    CachedLotteryConfig = config
end)