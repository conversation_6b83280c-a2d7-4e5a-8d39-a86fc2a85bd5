/* 彩票店管理系统样式 */

/* 字体优化 */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* 基础样式 */
:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --accent-color: #e74c3c;
    --dark-color: #2c3e50;
    --light-color: #ecf0f1;
    --text-color: #222;
    --border-color: #ddd;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

.admin-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    height: 80%;
    background-color: var(--light-color);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow);
    display: flex;
    flex-direction: column;
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', <PERSON>l, sans-serif;
    color: var(--text-color);
    font-size: 14px;
}

.hidden {
    display: none !important;
}

/* 头部样式 */
.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: var(--primary-color);
    color: white;
}

.admin-header h2 {
    margin: 0;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
}

.admin-header h2 i {
    margin-right: 10px;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition);
}

.close-btn:hover {
    transform: scale(1.1);
}

/* 导航样式 */
.admin-nav {
    display: flex;
    background-color: var(--dark-color);
    padding: 0 10px;
}

.nav-btn {
    padding: 15px 20px;
    background: none;
    border: none;
    color: white;
    font-size: 0.9rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    position: relative;
}

.nav-btn i {
    margin-right: 8px;
}

.nav-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-btn.active {
    background-color: var(--primary-color);
}

.nav-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--secondary-color);
}

/* 内容区域样式 */
.admin-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background-color: #f5f5f5;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 销售情况样式 */
.sales-summary {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
}

.summary-title {
    font-size: 0.9rem;
    color: #777;
    margin-bottom: 10px;
}

.summary-value {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--dark-color);
}

.summary-icon {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 1.5rem;
    opacity: 0.2;
    color: var(--primary-color);
}

/* 销售图表样式 */
.sales-chart-container {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: var(--shadow);
    margin-bottom: 30px;
}

.sales-chart-container h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: var(--dark-color);
    font-size: 1.2rem;
}

.chart-filters {
    display: flex;
    margin-bottom: 15px;
}

.chart-filter {
    background: none;
    border: 1px solid var(--border-color);
    padding: 8px 15px;
    margin-right: 10px;
    border-radius: 20px;
    cursor: pointer;
    transition: var(--transition);
}

.chart-filter:hover {
    background-color: #f0f0f0;
}

.chart-filter.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.sales-chart {
    height: 300px;
    width: 100%;
    background-color: #fafafa;
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

/* 表格样式 */
.sales-table-container, .record-table-container {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: var(--shadow);
    margin-bottom: 30px;
}

.sales-table-container h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: var(--dark-color);
    font-size: 1.2rem;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 15px;
}

.admin-table th, .admin-table td {
    padding: 14px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    font-weight: 500;
}

.admin-table th {
    background-color: #f5f5f5;
    font-weight: 600;
    color: var(--dark-color);
    font-size: 15px;
}

.admin-table tr:hover {
    background-color: #f9f9f9;
}

/* 记录过滤器样式 */
.record-filter {
    display: flex;
    margin-bottom: 20px;
}

.filter-btn {
    background: none;
    border: 1px solid var(--border-color);
    padding: 8px 15px;
    margin-right: 10px;
    border-radius: 20px;
    cursor: pointer;
    transition: var(--transition);
}

.filter-btn:hover {
    background-color: #f0f0f0;
}

.filter-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* 账户管理样式 */
.account-summary {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.account-card {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
}

.account-title {
    font-size: 0.9rem;
    color: #777;
    margin-bottom: 10px;
}

.account-value {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--dark-color);
}

.account-icon {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 1.5rem;
    opacity: 0.2;
    color: var(--primary-color);
}

.account-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.action-card {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: var(--shadow);
}

.action-card h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: var(--dark-color);
    font-size: 1.2rem;
}

.input-group {
    display: flex;
}

.input-group input {
    flex: 1;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px 0 0 4px;
    font-size: 1rem;
}

.input-group .action-btn {
    padding: 10px 15px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    transition: var(--transition);
}

.input-group .action-btn:hover {
    background-color: #2980b9;
}

/* 账户操作样式 */
.account-operations {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.operation-card {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: var(--shadow);
}

.operation-title {
    font-size: 1.1rem;
    font-weight: bold;
    color: var(--dark-color);
    margin-bottom: 15px;
}

.operation-content {
    display: flex;
    gap: 10px;
}

.operation-content input {
    flex: 1;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.2s ease;
    outline: none;
}

.operation-content input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.operation-content input:hover {
    border-color: #adb5bd;
}

.operation-content button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    cursor: pointer;
    transition: var(--transition);
}

.operation-content button:hover {
    background-color: var(--primary-hover);
}

/* 交易明细样式 */
.transaction-history {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: var(--shadow);
    margin-bottom: 30px;
}

.transaction-history h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: var(--dark-color);
    font-size: 1.2rem;
}

.income-text {
    color: #27ae60;
    font-weight: bold;
}

.expense-text {
    color: #e74c3c;
    font-weight: bold;
}

.empty-message {
    text-align: center;
    padding: 20px;
    color: #777;
    font-style: italic;
}

/* 加载动画 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .sales-summary, .account-summary {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .admin-container {
        width: 95%;
        height: 95%;
    }
    
    .sales-summary, .account-summary {
        grid-template-columns: 1fr;
    }
    
    .account-actions {
        grid-template-columns: 1fr;
    }
    
    .admin-nav {
        overflow-x: auto;
    }
    
    .account-summary, .account-operations {
        grid-template-columns: 1fr;
    }
}

/* 临时交易记录样式 */
.temp-transaction {
    animation: pulse 1.5s infinite;
    position: relative;
}

@keyframes pulse {
    0% {
        background-color: rgba(255, 255, 0, 0.05);
    }
    50% {
        background-color: rgba(255, 255, 0, 0.2);
    }
    100% {
        background-color: rgba(255, 255, 0, 0.05);
    }
}

/* 员工管理样式 */
.employees-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    position: relative;
}

.employee-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: background-color 0.3s;
}

.action-btn:hover {
    background-color: #45a049;
}

.cancel-btn {
    background-color: #f44336;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.cancel-btn:hover {
    background-color: #d32f2f;
}

/* 员工表格操作按钮 */
.employee-action-btn {
    background: none;
    border: none;
    color: #2196F3;
    cursor: pointer;
    margin-right: 5px;
    font-size: 16px;
    transition: color 0.3s;
}

.employee-action-btn:hover {
    color: #0b7dda;
}

.employee-action-btn.fire {
    color: #f44336;
}

.employee-action-btn.fire:hover {
    color: #d32f2f;
}

/* 员工状态标签 */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    text-align: center;
}

.status-active {
    background-color: #e8f5e9;
    color: #4CAF50;
}

.status-fired {
    background-color: #ffebee;
    color: #f44336;
}

/* 员工等级标签 */
.level-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-align: center;
}

.level-0 {
    background-color: #f5f5f5;
    color: #757575;
}

.level-1 {
    background-color: #e3f2fd;
    color: #2196F3;
}

.level-2 {
    background-color: #e8f5e9;
    color: #4CAF50;
}

.level-3 {
    background-color: #fff3e0;
    color: #ff9800;
}

.level-4 {
    background-color: #ede7f6;
    color: #673ab7;
}

.level-5 {
    background-color: #fce4ec;
    color: #e91e63;
}

/* 员工日志容器 */
.employee-logs-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    max-width: 1000px;
    max-height: 80vh;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.employee-logs-container.hidden {
    display: none;
}

.logs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
}

.logs-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

/* 对话框样式 */
.modal-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-dialog.hidden {
    display: none;
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    border-top: 1px solid #e0e0e0;
}

/* 表单样式 */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 15px;
    font-weight: 500;
}

.form-group textarea {
    min-height: 100px;
    resize: vertical;
}

/* 更新加载动画 */
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* 高亮更新的内容 */
.highlight-update {
    animation: highlight-pulse 2s ease-in-out;
}

@keyframes highlight-pulse {
    0% {
        background-color: rgba(255, 255, 0, 0.1);
    }
    50% {
        background-color: rgba(255, 255, 0, 0.3);
    }
    100% {
        background-color: transparent;
    }
}

/* 已解雇员工样式 */
.fired-employee {
    opacity: 0.7;
    background-color: rgba(200, 200, 200, 0.2);
}

.fired-employee td {
    text-decoration: line-through;
    color: #888;
}

/* 员工表格行的悬停效果 */
.employee-row:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

/* 新添加的员工行动画 */
@keyframes new-employee {
    from {
        background-color: rgba(40, 167, 69, 0.3);
    }
    to {
        background-color: transparent;
    }
}

/* 实时数据更新效果 */
.real-time-update {
    position: relative;
}

.real-time-update::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 193, 7, 0.2);
    opacity: 0;
    animation: flash 1s ease-out;
}

@keyframes flash {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

/* 表单帮助文本 */
.form-help {
    font-size: 0.85em;
    margin-top: 4px;
    color: #666;
}

.form-help.text-error {
    color: #e74c3c;
}

.form-help.text-success {
    color: #2ecc71;
}

.form-help.text-info {
    color: #3498db;
}

/* 验证状态样式 */
.valid-input {
    border: 2px solid #2ecc71 !important;
    animation: pulse-success 0.5s ease-in-out;
}

.error-input {
    border: 2px solid #e74c3c !important;
    animation: pulse-error 0.5s ease-in-out;
}

@keyframes pulse-success {
    0% { box-shadow: 0 0 0 0 rgba(46, 204, 113, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(46, 204, 113, 0); }
    100% { box-shadow: 0 0 0 0 rgba(46, 204, 113, 0); }
}

@keyframes pulse-error {
    0% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(231, 76, 60, 0); }
    100% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0); }
}

/* 表单组 */

/* 附近玩家选择样式 */
.nearby-players-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.player-select {
    flex: 1;
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #444;
    background-color: #2a2a2a;
    color: #fff;
    font-size: 14px;
}

.selected-player-info {
    margin-top: 10px;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    border-left: 3px solid #4CAF50;
}

.player-info-header {
    font-weight: bold;
    margin-bottom: 5px;
    color: #ccc;
}

.player-info-name {
    font-size: 16px;
    color: #fff;
    margin-bottom: 3px;
}

.player-info-id {
    font-size: 13px;
    color: #aaa;
}

#refresh-nearby-players {
    padding: 8px 12px;
    border-radius: 4px;
    background-color: #3b3b3b;
}

#refresh-nearby-players:hover {
    background-color: #4a4a4a;
}

/* 兑奖按钮样式 */
.claim-btn {
    background-color: var(--secondary-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.85rem;
}

.claim-btn:hover {
    background-color: #27ae60;
    transform: translateY(-2px);
}

.claim-btn:active {
    transform: translateY(0);
}

/* 手续费分成设置样式 */
.checkbox-wrapper {
    display: flex;
    align-items: center;
    margin-top: 5px;
}

.checkbox-wrapper input[type="checkbox"] {
    width: 20px;
    height: 20px;
    margin-right: 10px;
}

.form-group small.form-help {
    display: block;
    margin-top: 5px;
    font-size: 0.85em;
    color: #777;
}

/* 彩票配置样式 */
.lottery-config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.config-actions {
    display: flex;
    gap: 10px;
}

.lottery-config-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.config-section {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.config-section h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #ffcc33;
    border-bottom: 1px solid rgba(255, 204, 51, 0.3);
    padding-bottom: 8px;
}

.config-group {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.config-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.config-row label {
    flex: 1;
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.config-input-group {
    flex: 2;
    display: flex;
    align-items: center;
    position: relative;
}

.config-input-group input {
    width: 100%;
    padding: 10px 30px 10px 12px;
    border-radius: 4px;
    border: 1px solid #ccc;
    background-color: #fff;
    color: #333;
    font-size: 15px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.config-input-group input:focus {
    border-color: #3498db;
    outline: none;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.config-unit {
    position: absolute;
    right: 10px;
    color: #666;
    font-size: 15px;
    font-weight: 500;
    pointer-events: none;
}

/* 配置说明文字样式 */
.config-note {
    margin-top: 8px;
    margin-left: 0;
    font-size: 14px;
    color: #666;
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #17a2b8;
}

.config-note i {
    color: #17a2b8;
    font-size: 16px;
}

.config-save-btn {
    background-color: #27ae60;
    color: white;
    border: none;
    padding: 10px 18px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 15px;
    font-weight: 500;
    transition: all 0.3s ease;
    margin-top: 10px;
    align-self: flex-end;
}

.config-save-btn:hover {
    background-color: rgba(0, 128, 0, 0.9);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .lottery-config-container {
        grid-template-columns: 1fr;
    }
    
    .config-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .config-row label {
        margin-bottom: 5px;
    }
    
    .config-input-group {
        width: 100%;
    }
}

/* 奖项配置样式 */
.prize-config-container {
    margin-top: 20px;
    border-top: 1px solid var(--border-color);
    padding-top: 15px;
}

.prize-config-container h5 {
    color: var(--dark-color);
    font-size: 16px;
    margin-bottom: 15px;
}

.prize-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding-right: 5px;
    margin-bottom: 15px;
}

.prize-item {
    display: flex;
    align-items: center;
    background-color: #fff;
    padding: 10px;
    border-radius: 5px;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    margin-bottom: 5px;
}

.prize-item:hover {
    background-color: #f9f9f9;
}

.prize-name {
    flex: 2;
    font-size: 16px;
    font-weight: 500;
    color: var(--text-color);
}

.prize-match {
    flex: 2;
    font-size: 14px;
    font-weight: 500;
    color: #555;
}

.prize-amount-input {
    flex: 2;
    position: relative;
}

.prize-amount-input input {
    width: 100%;
    padding: 10px 25px 10px 10px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    background-color: #fff;
    color: var(--text-color);
    font-size: 15px;
    font-weight: 500;
}

.prize-amount-input span {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    font-size: 14px;
    font-weight: 500;
    pointer-events: none;
}

.prize-actions {
    display: flex;
    justify-content: flex-end;
}

.empty-prize-message {
    padding: 15px;
    text-align: center;
    color: #777;
    font-style: italic;
    background-color: #f9f9f9;
    border-radius: 5px;
}

/* 彩票配置标签样式 */
.lottery-type-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
    background-color: #fff;
    border-radius: 5px 5px 0 0;
    overflow: hidden;
}

.lottery-tab-btn {
    padding: 12px 20px;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    font-size: 16px;
    font-weight: 600;
    color: #555;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    text-align: center;
}

.lottery-tab-btn:hover {
    background-color: #f5f5f5;
    color: var(--dark-color);
}

.lottery-tab-btn.active {
    color: #2c3e50;
    border-bottom: 3px solid var(--primary-color);
    background-color: #f0f7ff;
    font-weight: 700;
}

.lottery-type-tab {
    display: none;
    animation: fadeIn 0.3s ease;
    background-color: white;
    border-radius: 0 0 5px 5px;
    padding: 20px;
    box-shadow: var(--shadow);
}

.lottery-type-tab.active {
    display: block;
}

/* 调整配置区域样式 */
.lottery-config-container {
    background-color: #f5f5f5;
    border-radius: 8px;
    overflow: hidden;
}

.config-section {
    margin-bottom: 20px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .lottery-tab-btn {
        padding: 10px 15px;
        font-size: 13px;
    }
}

/* 开奖设置样式 */
.draw-days-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 5px;
}

.draw-day-item {
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    padding: 5px 10px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

.draw-day-item input[type="checkbox"] {
    margin-right: 5px;
}

.draw-day-item label {
    margin: 0;
    font-size: 0.9rem;
    cursor: pointer;
}

@media (max-width: 768px) {
    .draw-days-container {
        flex-direction: column;
        gap: 5px;
    }
}

/* 权重配置样式 */
.amount-rates-container {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e9ecef;
    margin-bottom: 15px;
}

.rate-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    margin-bottom: 8px;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.rate-item:hover {
    border-color: #3498db;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.15);
    transform: translateY(-1px);
}

/* 当下拉框打开时，确保rate-item有足够高的层级 */
.rate-item:has(.item-dropdown[style*="block"]) {
    position: relative;
    z-index: 1001;
}

/* 兼容性备选方案 - 为打开的下拉框的父容器设置高层级 */
.rate-item.dropdown-open {
    position: relative;
    z-index: 1001;
}

.rate-key {
    flex: 1;
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    min-width: 120px;
    display: flex;
    align-items: center;
}

.rate-key::before {
    content: "💰";
    margin-right: 8px;
    font-size: 18px;
}

.rate-value-input {
    flex: 2;
    max-width: 200px;
}

.rate-value-input input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 15px;
    font-weight: 500;
    color: #495057;
    background-color: #fff;
    transition: all 0.2s ease;
}

.rate-value-input input:focus {
    border-color: #3498db;
    outline: none;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.rate-value-input input:hover {
    border-color: #adb5bd;
}

.empty-rates-message {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
    font-size: 16px;
    font-style: italic;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 2px dashed #dee2e6;
}

/* 权重配置标题样式 */
.prize-config-container h5 {
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #3498db;
    position: relative;
}

.prize-config-container h5::before {
    content: "⚙️";
    margin-right: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .rate-item {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .rate-key {
        min-width: auto;
        justify-content: center;
        text-align: center;
    }

    .rate-value-input {
        max-width: none;
    }
}

/* 不同类型权重配置的特殊样式 */
#scratch-xixiangfeng-amount-rates .rate-key::before {
    content: "🎊";
}

#scratch-fusong-row-amount-rates .rate-key::before {
    content: "🐭";
}

#scratch-fusong-match-rates .rate-key::before {
    content: "🔢";
}

#scratch-yaocai-winning-amount-rates .rate-key::before {
    content: "✨";
}

#scratch-yaocai-match-rates .rate-key::before {
    content: "🎯";
}

#scratch-caizuan-winning-item-rates .rate-key::before {
    content: "💎";
}

#scratch-caizuan-match-rates .rate-key::before {
    content: "🔢";
}

#scratch-caizuan-diamond-rates .rate-key::before {
    content: "💎";
}

/* 物品选择器样式 */
.rate-key.item-selector {
    position: relative;
}

.item-selector-container {
    position: relative;
    width: 100%;
    z-index: 1000;
}

.item-display-button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    color: #495057;
}

.item-display-button:hover {
    background-color: #e9ecef;
    border-color: #3498db;
}

.item-display-button .item-name {
    flex: 1;
    text-align: left;
    font-weight: 500;
}

.item-display-button i {
    margin-left: 8px;
    transition: transform 0.2s ease;
}

.item-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    max-height: 300px;
    overflow-y: auto; /* 保持垂直滚动 */
}

/* 当下拉框接近底部时，向上展开 */
.item-dropdown.dropdown-up {
    top: auto;
    bottom: 100%;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.15);
}

/* 确保下拉框的父容器不会裁剪下拉框 */
.item-selector-container {
    position: relative;
    overflow: visible !important;
}

.rate-item {
    position: relative;
    overflow: visible !important;
}

/* 为物品配置容器设置足够的空间 */
.amount-rates-container {
    padding-bottom: 50px; /* 增加底部空间 */
    overflow: visible !important;
}

/* 确保所有可能影响下拉框显示的父容器都不裁剪内容 */
.prize-config-container {
    overflow: visible !important;
}

.lottery-type-tab {
    overflow: visible !important;
}

/* 提高下拉框的z-index确保它在最顶层 */
.item-dropdown {
    z-index: 99999 !important;
}

.item-option {
    padding: 10px 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #f1f3f4;
}

.item-option:last-child {
    border-bottom: none;
}

.item-option:hover {
    background-color: #f8f9fa;
}

.item-option.selected {
    background-color: #e3f2fd;
    color: #1976d2;
}

.item-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.item-info .item-name {
    font-weight: 500;
    color: #2c3e50;
}

.item-info .item-code {
    font-size: 12px;
    color: #6c757d;
    background-color: #f1f3f4;
    padding: 2px 6px;
    border-radius: 3px;
}

.money-amount-input {
    width: 80px;
    padding: 4px 6px;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    font-size: 12px;
    text-align: center;
}

.money-amount-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* 添加新物品按钮样式 */
.add-new-item-button {
    margin: 15px 0;
    text-align: center;
}

.btn-add-item {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
}

.btn-add-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.btn-add-item i {
    margin-right: 6px;
}

/* 删除按钮样式 */
.btn-delete-item {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 6px 8px;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 8px;
    transition: all 0.2s ease;
    font-size: 12px;
}

.btn-delete-item:hover {
    background-color: #c82333;
    transform: scale(1.05);
}

/* 新物品项的特殊样式 */
.rate-item.new-item {
    border: 2px dashed #28a745;
    background-color: #f8fff9;
}

.rate-item.new-item .item-display-button {
    background-color: #e8f5e9;
    border-color: #28a745;
}

/* 权重输入容器调整 */
.rate-value-input {
    display: flex;
    align-items: center;
}

/* 权重配置容器的特殊背景 */
#scratch-xixiangfeng-amount-rates {
    background: linear-gradient(135deg, #fff5f5 0%, #ffe8e8 100%);
    border-left: 4px solid #ff6b6b;
}

#scratch-fusong-row-amount-rates,
#scratch-fusong-match-rates {
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
    border-left: 4px solid #3498db;
}

#scratch-yaocai-winning-amount-rates,
#scratch-yaocai-match-rates {
    background: linear-gradient(135deg, #f0fff4 0%, #e6ffe6 100%);
    border-left: 4px solid #2ecc71;
}

#scratch-caizuan-winning-item-rates {
    background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
    border-left: 4px solid #9c27b0;
}

#scratch-caizuan-match-rates {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    border-left: 4px solid #4caf50;
}

#scratch-caizuan-diamond-rates {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-left: 4px solid #2196f3;
}

/* 权重值的视觉提示 */
.rate-value-input input[value="0"],
.rate-value-input input:placeholder-shown {
    background-color: #fff3cd;
    border-color: #ffc107;
}

.rate-value-input input:not([value="0"]):not(:placeholder-shown) {
    background-color: #d4edda;
    border-color: #28a745;
}

/* 保存按钮的增强样式 */
.prize-actions .config-save-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
    transition: all 0.3s ease;
}

.prize-actions .config-save-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
    background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
}

.prize-actions .config-save-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

/* 权重配置加载动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.rate-item {
    animation: fadeInUp 0.3s ease forwards;
}

.rate-item:nth-child(1) { animation-delay: 0.1s; }
.rate-item:nth-child(2) { animation-delay: 0.2s; }
.rate-item:nth-child(3) { animation-delay: 0.3s; }
.rate-item:nth-child(4) { animation-delay: 0.4s; }
.rate-item:nth-child(5) { animation-delay: 0.5s; }
.rate-item:nth-child(n+6) { animation-delay: 0.6s; }

/* 权重配置标题的增强样式 */
.amount-rates-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3498db, #2ecc71, #f39c12, #e74c3c);
    border-radius: 8px 8px 0 0;
}

.amount-rates-container {
    position: relative;
    overflow: hidden;
}

/* 输入框的数值验证样式 */
.rate-value-input input:invalid {
    border-color: #dc3545;
    background-color: #f8d7da;
}

.rate-value-input input:valid:not(:placeholder-shown) {
    border-color: #28a745;
    background-color: #d4edda;
}

/* 权重配置的统计信息 */
.rates-summary {
    background-color: #e9ecef;
    border-radius: 6px;
    padding: 12px 16px;
    margin-top: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: #495057;
}

.rates-summary .summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.rates-summary .summary-label {
    font-weight: 600;
    color: #6c757d;
}

.rates-summary .summary-value {
    font-weight: 700;
    color: #2c3e50;
    font-size: 16px;
}

/* 排列5物品配置样式 - 使其与彩钻样式一致 */
#arrange-five-items.item-config-list {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e9ecef;
    margin-bottom: 15px;
    position: relative;
    overflow: hidden;
}

/* 为排列5物品配置添加顶部装饰条 */
#arrange-five-items.item-config-list::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3498db, #2ecc71, #f39c12, #e74c3c);
    border-radius: 8px 8px 0 0;
}

/* 排列5物品配置头部样式 */
#arrange-five-tab .item-config-header {
    display: none; /* 隐藏表头，使其更像彩钻样式 */
}

/* 排列5物品配置操作按钮样式 */
#arrange-five-tab .item-config-actions {
    margin-top: 15px;
    text-align: center;
}

#arrange-five-tab .config-add-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
}

#arrange-five-tab .config-add-btn:hover {
    background: linear-gradient(135deg, #218838, #1ea085);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}