-- 数据库修复脚本
-- 用于修复MySQL存储过程相关问题

local function SystemPrint(message, level)
    level = level or "^7" -- 默认白色
    print(level .. "[彩票系统-修复] " .. message)
end

-- 检查MySQL版本
function CheckMySQLVersion()
    SystemPrint('^3正在检查MySQL版本...')
    
    MySQL.Async.fetchScalar('SELECT VERSION()', {}, function(version)
        if version then
            SystemPrint('^2MySQL版本: ' .. tostring(version))
            
            local majorVersion = tonumber(string.match(version, "^(%d+)"))
            if majorVersion and majorVersion >= 8 then
                SystemPrint('^3检测到MySQL 8.0+，建议使用兼容模式')
                return "8+"
            else
                SystemPrint('^3检测到MySQL 5.x')
                return "5.x"
            end
        else
            SystemPrint('^1无法获取MySQL版本信息')
            return nil
        end
    end)
end

-- 检查存储过程是否存在
function CheckStoredProcedures()
    SystemPrint('^3正在检查存储过程...')
    
    MySQL.Async.fetchAll([[
        SELECT ROUTINE_NAME 
        FROM INFORMATION_SCHEMA.ROUTINES 
        WHERE ROUTINE_SCHEMA = DATABASE() 
        AND ROUTINE_TYPE = 'PROCEDURE'
        AND ROUTINE_NAME = 'CleanupOldLotteryData'
    ]], {}, function(result)
        if result and #result > 0 then
            SystemPrint('^2存储过程 CleanupOldLotteryData 已存在')
        else
            SystemPrint('^3存储过程 CleanupOldLotteryData 不存在')
        end
    end)
end

-- 检查事件调度器状态
function CheckEventScheduler()
    SystemPrint('^3正在检查事件调度器状态...')
    
    MySQL.Async.fetchScalar('SELECT @@event_scheduler', {}, function(result)
        if result then
            SystemPrint('^2事件调度器状态: ' .. tostring(result))
            if tostring(result):upper() == "ON" then
                SystemPrint('^2事件调度器已启用')
            else
                SystemPrint('^3事件调度器未启用')
            end
        else
            SystemPrint('^1无法获取事件调度器状态')
        end
    end)
end

-- 检查数据库表结构
function CheckTableStructure()
    SystemPrint('^3正在检查数据库表结构...')
    
    local tables = {
        'lottery_tickets',
        'scratch_cards', 
        'prize_pools',
        'draw_history',
        'lottery_announcements',
        'lottery_shop_accounts',
        'lottery_transactions',
        'lottery_employees',
        'lottery_admin_logs',
        'lottery_offline_prizes'
    }
    
    for _, tableName in ipairs(tables) do
        MySQL.Async.fetchScalar('SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?', {tableName}, function(result)
            if result and tonumber(result) > 0 then
                SystemPrint('^2表 ' .. tableName .. ' 存在')
            else
                SystemPrint('^1表 ' .. tableName .. ' 不存在')
            end
        end)
    end
end

-- 修复mysql.proc表问题（适用于MySQL 5.x）
function RepairMySQLProc()
    SystemPrint('^3尝试修复mysql.proc表...')
    
    -- 检查是否有权限访问mysql.proc表
    MySQL.Async.fetchScalar('SELECT COUNT(*) FROM mysql.proc LIMIT 1', {}, function(result)
        if result then
            SystemPrint('^2mysql.proc表可以访问')
            
            -- 尝试修复表
            MySQL.Async.execute('REPAIR TABLE mysql.proc', {}, function(success)
                if success then
                    SystemPrint('^2mysql.proc表修复成功')
                else
                    SystemPrint('^1mysql.proc表修复失败')
                end
            end)
        else
            SystemPrint('^1无法访问mysql.proc表，可能是权限问题或MySQL版本问题')
        end
    end)
end

-- 清理损坏的存储过程
function CleanupCorruptedProcedures()
    SystemPrint('^3正在清理可能损坏的存储过程...')
    
    -- 使用更安全的方式删除存储过程
    MySQL.Async.execute('DROP PROCEDURE IF EXISTS CleanupOldLotteryData', {}, function(success)
        if success then
            SystemPrint('^2已删除存储过程 CleanupOldLotteryData')
        else
            SystemPrint('^3删除存储过程失败或不存在')
        end
        
        -- 删除相关事件
        MySQL.Async.execute('DROP EVENT IF EXISTS cleanup_lottery_data', {}, function(eventSuccess)
            if eventSuccess then
                SystemPrint('^2已删除事件 cleanup_lottery_data')
            else
                SystemPrint('^3删除事件失败或不存在')
            end
        end)
    end)
end

-- 重新创建存储过程（兼容模式）
function RecreateStoredProcedure()
    SystemPrint('^3正在重新创建存储过程（兼容模式）...')
    
    local procedureSQL = [[
        CREATE PROCEDURE CleanupOldLotteryData()
        BEGIN
            DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
            BEGIN
                GET DIAGNOSTICS CONDITION 1
                @sqlstate = RETURNED_SQLSTATE, 
                @errno = MYSQL_ERRNO, 
                @text = MESSAGE_TEXT;
                SELECT CONCAT('错误: ', @errno, ' (', @sqlstate, '): ', @text) AS error_message;
            END;

            -- 删除30天前的已兑奖彩票记录
            DELETE FROM lottery_tickets
            WHERE is_claimed = 1
            AND claimed_time < DATE_SUB(NOW(), INTERVAL 30 DAY)
            LIMIT 1000;

            -- 删除30天前的已刮开且已兑奖的刮刮乐记录
            DELETE FROM scratch_cards
            WHERE is_scratched = 1
            AND is_claimed = 1
            AND claimed_time < DATE_SUB(NOW(), INTERVAL 30 DAY)
            LIMIT 1000;

            -- 删除90天前的管理日志
            DELETE FROM lottery_admin_logs
            WHERE timestamp < DATE_SUB(NOW(), INTERVAL 90 DAY)
            LIMIT 1000;

            -- 删除90天前的交易记录
            DELETE FROM lottery_transactions
            WHERE timestamp < DATE_SUB(NOW(), INTERVAL 90 DAY)
            LIMIT 1000;

            -- 删除7天前的中奖公告
            DELETE FROM lottery_announcements
            WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY)
            LIMIT 1000;

            SELECT '数据清理完成' AS result;
        END
    ]]
    
    MySQL.Async.execute(procedureSQL, {}, function(success)
        if success then
            SystemPrint('^2存储过程创建成功')
        else
            SystemPrint('^1存储过程创建失败')
        end
    end)
end

-- 执行完整的数据库修复
function PerformDatabaseRepair()
    SystemPrint('^3开始执行数据库修复...')
    
    -- 步骤1: 检查MySQL版本
    CheckMySQLVersion()
    
    -- 步骤2: 检查表结构
    SetTimeout(1000, function()
        CheckTableStructure()
    end)
    
    -- 步骤3: 清理损坏的存储过程
    SetTimeout(2000, function()
        CleanupCorruptedProcedures()
    end)
    
    -- 步骤4: 检查事件调度器
    SetTimeout(3000, function()
        CheckEventScheduler()
    end)
    
    -- 步骤5: 重新创建存储过程
    SetTimeout(4000, function()
        RecreateStoredProcedure()
    end)
    
    -- 步骤6: 验证修复结果
    SetTimeout(5000, function()
        CheckStoredProcedures()
        SystemPrint('^2数据库修复完成')
    end)
end

-- 导出函数
exports('CheckMySQLVersion', CheckMySQLVersion)
exports('CheckStoredProcedures', CheckStoredProcedures)
exports('CheckEventScheduler', CheckEventScheduler)
exports('CheckTableStructure', CheckTableStructure)
exports('RepairMySQLProc', RepairMySQLProc)
exports('CleanupCorruptedProcedures', CleanupCorruptedProcedures)
exports('RecreateStoredProcedure', RecreateStoredProcedure)
exports('PerformDatabaseRepair', PerformDatabaseRepair)

-- 注册命令（仅管理员可用）
RegisterCommand('lottery_repair', function(source, args, rawCommand)
    if source == 0 then -- 服务器控制台
        PerformDatabaseRepair()
    else
        -- 检查玩家权限
        local xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer and xPlayer.getGroup() == 'admin' then
            PerformDatabaseRepair()
        else
            print('权限不足')
        end
    end
end, true)

SystemPrint('^2数据库修复脚本已加载，使用 /lottery_repair 命令执行修复')
